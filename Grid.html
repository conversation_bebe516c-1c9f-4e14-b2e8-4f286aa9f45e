<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="A sample landing page with most common HTML tags.">
  <title>Grid</title>
  <link rel="stylesheet" href="assets/css/style.css">
  <link rel="stylesheet" href="assets/css/grid.css">
</head>

<body>

    <nav class="main-header">
        <ul>
            <li><a href="index.html">Home</a></li>
            <li><a href="about.html">About</a></li>
            <li><a href="flex.html">Flex</a></li>
            <li><a href="grid.html">Grid</a></li>
        </ul>
    </nav>


    <header>تعليم CSS Grid</header>

    <section class="section">
        <h2>1. Grid Template Columns (3 عناصر في السطر)</h2>
        <div class="grid-3">
            <div class="box">1</div>
            <div class="box">2</div>
            <div class="box">3</div>
            <div class="box">4</div>
            <div class="box">5</div>
            <div class="box">6</div>
        </div>
    </section>

    <section class="section">
        <h2>2. Grid with Auto-fit + Minmax</h2>
        <div class="grid-auto">
            <div class="box">🍎</div>
            <div class="box">🍊</div>
            <div class="box">🍌</div>
            <div class="box">🍉</div>
            <div class="box">🍇</div>
        </div>
    </section>

    <section class="section">
        <h2>3. Grid Areas</h2>
        <div class="grid-layout">
            <div class="area header">رأس</div>
            <div class="area sidebar">قائمة</div>
            <div class="area main">محتوى</div>
            <div class="area footer">تذييل</div>
        </div>
    </section>

    <footer>© 2025 - تدريب على Grid</footer>

</body>

</html>