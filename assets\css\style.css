/* Reset & base styles */
* {
  margin: 0;
  padding: 0;                                                                             
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  padding: 20px;
}

/* Header */
header {
  background-color: #29cc4c;
  color: white;
  padding: 20px;
  border-radius: 8px;
}

header h1 {
  margin-bottom: 10px;
}

nav ul {
  display: flex;
  gap: 20px;
  list-style: none;
}

nav a {
  color: white;
  text-decoration: none;
  font-weight: 500;
}

/* Main content layout */
main {
  margin-top: 20px;
  display: grid;
  gap: 20px;
}

/* Sections, articles, aside */
section,
article,
aside {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin-top: 10px;
}

/* Typography enhancements */
h2, h3 {
  margin-bottom: 10px;
  color: #333;
}

p {
  margin-bottom: 10px;
}

/* Lists */
ul, ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

li {
  margin-bottom: 5px;
}

/* Blockquote */
blockquote {
  padding: 10px 20px;
  margin: 10px 0;
  background-color: #eee;
  border-left: 5px solid #6200ea;
  font-style: italic;
}

/* Code */
code {
  background-color: #eee;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

pre {
  background-color: #eee;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* Form */
form {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
}

fieldset {
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 6px;
}

legend {
  padding: 0 10px;
  font-weight: bold;
}

label {
  display: block;
  margin-top: 10px;
  margin-bottom: 5px;
}

input[type="text"],
input[type="email"],
textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
}

input[type="submit"] {
  margin-top: 10px;
  background-color: #6200ea;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}

input[type="submit"]:hover {
  background-color: #4500b5;
}

/* Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

caption {
  font-weight: bold;
  margin-bottom: 10px;
}

th, td {
  padding: 10px;
  border: 1px solid #ccc;
  text-align: left;
}

thead {
  background-color: #eeeeee;
}

/* Footer */
footer {
  margin-top: 30px;
  text-align: center;
  color: #777;
}
