body {
  margin: 0;
  font-family: 'Arial', sans-serif;
  background-color: #f4f4f4;
  direction: rtl;
}

header,
footer {
  background-color: #00796b;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 1.5em;
}

.section {
  margin: 20px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

h2 {
  margin-bottom: 10px;
  color: #00796b;
}

/* Flex Row & Column */
.flex-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.flex-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Justify Content */
.flex {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

/* Align Items */
.tall {
  height: 150px;
  border: 1px solid #ccc;
}

.align-start {
  align-items: flex-start;
}

/* Wrap + Gap */
.wrap {
  flex-wrap: wrap;
}

.gap {
  gap: 15px;
}

/* Flex Grow & Shrink */
.grow-demo {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.grow-demo > div {
  flex: 0 0 calc(33.333% - 10px); /* 3 عناصر في السطر مع فاصل */
  box-sizing: border-box;
}

.grow1 {
  flex-grow: 1;
}

.grow2 {
  flex-grow: 2;
}

/* Boxes */
.box {
  background-color: #4db6ac;
  color: white;
  text-align: center;
  padding: 20px;
  border-radius: 6px;
  min-width: 60px;
  min-height: 60px;
  box-sizing: border-box;
}
