<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="A sample landing page with most common HTML tags.">
  <title>Flex</title>
  <link rel="stylesheet" href="assets/css/flex.css">
  <link rel="stylesheet" href="assets/css/style.css">
</head>

<body>

  <nav class="main-header">
    <ul>
      <li><a href="index.html">Home</a></li>
      <li><a href="about.html">About</a></li>
      <li><a href="flex.html">Flex</a></li>
      <li><a href="grid.html">Grid</a></li>
    </ul>
  </nav>

  <header>تعليم Flexbox</header>


  <section class="section">

    <h2>1. Flex Direction</h2>


    <div class="flex-row">
      <div class="box">A</div>
      <div class="box">B</div>
      <div class="box">C</div>
    </div>

    <div class="flex-column">
      <div class="box">1</div>
      <div class="box">2</div>
      <div class="box">3</div>
    </div>

  </section>





  <section class="section">
    <h2>2. Justify Content</h2>

    <div class="flex justify-between">
      <div class="box">🍎</div>
      <div class="box">🍊</div>
      <div class="box">🍌</div>
    </div>

    <div class="flex justify-center">
      <div class="box">😀</div>
      <div class="box">😎</div>
      <div class="box">😡</div>
    </div>
  </section>



  <section class="section" id="justify">
    <h2>3. Align Items</h2>
    <div class="flex tall control">
      <div class="box">Top</div>
      <div class="box">Middle</div>
      <div class="box">Bottom</div>
    </div>
  </section>

  <section class="section">
    <h2>4. Wrap + Gap</h2>

    <div class="flex wrap gap">
      <div class="box">1</div>
      <div class="box">2</div>
      <div class="box">3</div>
      <div class="box">4</div>
      <div class="box">5</div>
      <div class="box">6</div>

    </div>
  </section>


  <section class="section">
    <h2>5. Flex Grow & Shrink</h2>
    <div class="flex grow-demo wrap">
      <div class="box grow1">Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolorum, ullam!</div>
      <div class="box grow2">Lorem ipsum dolor sit amet consectetur adipisicing elit. Ipsam, temporibus?</div>
      <div class="box">Lorem ipsum dolor sit amet consectetur adipisicing elit. Ea, quia.</div>
      <div class="box">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Officia, culpa?</div>
      <div class="box">Lorem ipsum dolor sit amet consectetur adipisicing elit. Amet, nihil.</div>
      <div class="box">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Voluptate, blanditiis.</div>
    </div>
  </section>

  <footer>© 2025 - تدريب على Flexbox</footer>

</body>

</html>