body {
  margin: 0;
  font-family: 'Arial', sans-serif;
  background-color: #f4f4f4;
  direction: rtl;
}

header,
footer {
  background-color: #3f51b5;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 1.5em;
}

.section {
  margin: 20px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

h2 {
  margin-bottom: 10px;
  color: #3f51b5;
}

/* Grid - 3 أعمدة */
.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

/* Auto-fit + minmax for responsiveness */
.grid-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 15px;
}

/* Grid Layout with Named Areas */
.grid-layout {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar main"
    "footer footer";
  grid-template-columns: 1fr 2fr;
  gap: 10px;
  margin-top: 20px;
}

.area {
  background-color: #c5cae9;
  padding: 20px;
  border-radius: 6px;
  color: #333;
  text-align: center;
}

.header {
  grid-area: header;
  background-color: #3f51b5;
  color: white;
}

.sidebar {
  grid-area: sidebar;
}

.main {
  grid-area: main;
}

.footer {
  grid-area: footer;
  background-color: #303f9f;
  color: white;
}

/* عناصر الصناديق */
.box {
  background-color: #7986cb;
  color: white;
  text-align: center;
  padding: 20px;
  border-radius: 6px;
  min-height: 60px;
  box-sizing: border-box;
}
